/**
 * Gemini Live Audio Service for real-time companion voice interaction
 * Based on the working example from Gemini Live API documentation
 */

import { GoogleGenAI, LiveServerMessage, Modality, StartSensitivity, EndSensitivity } from '@google/genai';

// Check if we have the API key
const API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
console.log('Gemini Live Audio - API Key check:', API_KEY ? 'Present' : 'Missing');

if (!API_KEY) {
  console.error('VITE_GEMINI_API_KEY is not set. Available env vars:', Object.keys(import.meta.env));
  throw new Error('VITE_GEMINI_API_KEY is not set. Gemini Live Audio requires an API key.');
}

export interface LiveAudioConfig {
  model?: string;
  responseModalities?: Modality[];
  interruptSensitivity?: StartSensitivity;
}

export interface LiveAudioCallbacks {
  onopen: () => void;
  onmessage: (message: LiveServerMessage) => void;
  onerror: (error: Error) => void;
  onclose: (reason?: string) => void;
  onAudioReceived?: (audioBuffer: AudioBuffer) => void;
  onTextReceived?: (text: string) => void;
}

export class GeminiLiveAudioService {
  private client: GoogleGenAI;
  private session: any = null;
  private inputAudioContext: AudioContext | null = null;
  private outputAudioContext: AudioContext | null = null;
  private callbacks: LiveAudioCallbacks | null = null;
  private responseQueue: LiveServerMessage[] = [];
  private activeSources: AudioBufferSourceNode[] = [];
  private isPlayingAudio: boolean = false;

  private nextStartTime: number = 0;
  private mediaStream: MediaStream | null = null;
  private sourceNode: MediaStreamAudioSourceNode | null = null;
  private scriptProcessorNode: ScriptProcessorNode | null = null;
  private isRecording: boolean = false;

  constructor() {
    this.client = new GoogleGenAI({ apiKey: API_KEY });
  }

  /**
   * Initialize audio contexts
   */
  private initAudioContexts(): void {
    if (!this.inputAudioContext || this.inputAudioContext.state === 'closed') {
      this.inputAudioContext = new (window.AudioContext || (window as any).webkitAudioContext)({ sampleRate: 16000 });
    }
    if (!this.outputAudioContext || this.outputAudioContext.state === 'closed') {
      this.outputAudioContext = new (window.AudioContext || (window as any).webkitAudioContext)({ sampleRate: 24000 });
    }
  }

  /**
   * Request microphone access
   */
  private async requestMicrophoneAccess(): Promise<void> {
    try {
      console.log('Requesting microphone access...');
      this.mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true, video: false });
      console.log('Microphone access granted');
    } catch (error) {
      console.error('Error requesting microphone access:', error);
      throw new Error(`Microphone access denied: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert Float32Array to Blob for sending to Gemini
   */
  private createBlob(pcmData: Float32Array): Blob {
    const int16Array = new Int16Array(pcmData.length);
    for (let i = 0; i < pcmData.length; i++) {
      int16Array[i] = Math.max(-32768, Math.min(32767, pcmData[i] * 32768));
    }
    return new Blob([int16Array.buffer], { type: 'audio/pcm' });
  }

  /**
   * Start microphone input processing
   */
  private startMicrophoneInput(): void {
    console.log('startMicrophoneInput called - checking dependencies...');
    console.log('  mediaStream:', this.mediaStream ? 'OK' : 'NULL');
    console.log('  inputAudioContext:', this.inputAudioContext ? 'OK' : 'NULL');
    console.log('  session:', this.session ? 'OK' : 'NULL');

    if (!this.mediaStream) {
      console.error('Cannot start microphone input: mediaStream is null');
      return;
    }
    if (!this.inputAudioContext) {
      console.error('Cannot start microphone input: inputAudioContext is null');
      return;
    }
    if (!this.session) {
      console.error('Cannot start microphone input: session is null');
      return;
    }

    try {
      this.sourceNode = this.inputAudioContext.createMediaStreamSource(this.mediaStream);

      const bufferSize = 4096;
      this.scriptProcessorNode = this.inputAudioContext.createScriptProcessor(bufferSize, 1, 1);

      this.scriptProcessorNode.onaudioprocess = (audioProcessingEvent) => {
        if (!this.isRecording || !this.session) return;

        const inputBuffer = audioProcessingEvent.inputBuffer;
        const pcmData = inputBuffer.getChannelData(0);

        // Send raw audio data to Gemini
        this.session.sendRealtimeInput(this.createBlob(pcmData));
      };

      this.sourceNode.connect(this.scriptProcessorNode);
      this.scriptProcessorNode.connect(this.inputAudioContext.destination);

      this.isRecording = true;
      console.log('Microphone input started');
    } catch (error) {
      console.error('Failed to start microphone input:', error);
    }
  }

  /**
   * Stop microphone input processing
   */
  private stopMicrophoneInput(): void {
    this.isRecording = false;

    if (this.scriptProcessorNode) {
      this.scriptProcessorNode.disconnect();
      this.scriptProcessorNode = null;
    }

    if (this.sourceNode) {
      this.sourceNode.disconnect();
      this.sourceNode = null;
    }

    console.log('Microphone input stopped');
  }

  /**
   * Connect to Gemini Live Audio session
   */
  async connect(config: LiveAudioConfig, callbacks: LiveAudioCallbacks): Promise<void> {
    try {
      this.callbacks = callbacks;

      console.log('Connecting to Gemini Live Audio...');

      // Initialize audio contexts and request microphone
      this.initAudioContexts();
      await this.requestMicrophoneAccess();

      const model = config.model || 'gemini-2.0-flash-live-001';

      console.log('Connecting with model:', model);
      console.log('Config responseModalities:', config.responseModalities);

      const session = await this.client.live.connect({
        model: model,
        callbacks: {
          onopen: () => {
            console.log('Gemini Live Audio session opened');
            callbacks.onopen();
          },
          onmessage: (message: LiveServerMessage) => {
            console.log('Received message:', message);
            this.responseQueue.push(message);
            this.handleMessage(message);
            callbacks.onmessage(message);
          },
          onerror: (error: any) => {
            console.error('Gemini Live Audio error:', error);
            callbacks.onerror(new Error(error.message || 'Live audio error'));
          },
          onclose: (event: any) => {
            console.log('Gemini Live Audio session closed:', event?.reason);
            this.cleanup();
            callbacks.onclose(event?.reason);
          }
        },
        config: {
          responseModalities: config.responseModalities || [Modality.AUDIO],
          realtimeInputConfig: {
            automaticActivityDetection: {
              disabled: false,
              startOfSpeechSensitivity: config.interruptSensitivity || StartSensitivity.START_SENSITIVITY_HIGH,
              endOfSpeechSensitivity: EndSensitivity.END_SENSITIVITY_HIGH,
            },
          },
        }
      });

      // Assign session after successful connection
      this.session = session;
      console.log('Gemini Live Audio connected successfully');

    } catch (error) {
      console.error('Failed to connect to Gemini Live Audio:', error);
      throw new Error(`Live audio connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Send text message to the session
   */
  sendText(text: string): void {
    if (!this.session) {
      console.warn('No active session to send text');
      return;
    }

    try {
      console.log('Attempting to send text to Gemini Live:', text);
      this.session.sendClientContent({ turns: text });
      console.log('Successfully sent text to Gemini Live:', text);
    } catch (error) {
      console.error('Failed to send text:', error);
      throw error;
    }
  }

  /**
   * Handle incoming messages from Gemini Live - stream audio chunks as they arrive
   */
  private handleMessage(message: LiveServerMessage): void {
    try {
      // Check for interruption first
      const interrupted = message.serverContent?.interrupted;
      if (interrupted) {
        console.log('Interruption detected, stopping all audio');
        this.stopAllAudio();
        return;
      }

      // Handle audio responses by streaming them immediately
      if (message.serverContent?.modelTurn?.parts) {
        for (const part of message.serverContent.modelTurn.parts) {
          // Handle audio response - stream each chunk immediately
          if (part.inlineData?.mimeType?.startsWith('audio/') && part.inlineData.data) {
            console.log('Streaming audio chunk from Gemini Live');
            this.streamAudioChunk(part.inlineData.data);
          }
        }
      }

      // Check for turn completion to notify callbacks
      if (message.serverContent?.turnComplete) {
        console.log('Turn complete detected');
        // Notify that audio streaming has finished
        if (this.isPlayingAudio) {
          console.log('Audio streaming completed');
        }
      }
    } catch (error) {
      console.error('Error handling message:', error);
    }
  }

  /**
   * Stream a single audio chunk immediately
   */
  private async streamAudioChunk(base64Audio: string): Promise<void> {
    try {
      if (!this.outputAudioContext) {
        this.outputAudioContext = new (window.AudioContext || (window as any).webkitAudioContext)({ sampleRate: 24000 });
      }

      if (this.outputAudioContext.state === 'suspended') {
        await this.outputAudioContext.resume();
      }

      // Convert base64 to audio buffer
      const binaryString = atob(base64Audio);
      const audioBytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        audioBytes[i] = binaryString.charCodeAt(i);
      }
      const intArray = new Int16Array(audioBytes.buffer, audioBytes.byteOffset, audioBytes.byteLength / Int16Array.BYTES_PER_ELEMENT);

      // Create audio buffer (24kHz, mono, 16-bit PCM)
      const audioBuffer = this.outputAudioContext.createBuffer(1, intArray.length, 24000);
      const channelData = audioBuffer.getChannelData(0);

      // Convert Int16 to Float32 and copy to audio buffer
      for (let i = 0; i < intArray.length; i++) {
        channelData[i] = intArray[i] / 32768.0; // Convert to [-1, 1] range
      }

      // Schedule the audio chunk to play at the right time
      const source = this.outputAudioContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(this.outputAudioContext.destination);

      // Calculate when to start this chunk
      const currentTime = this.outputAudioContext.currentTime;

      if (!this.isPlayingAudio) {
        // First chunk - start immediately and notify callbacks
        this.isPlayingAudio = true;

        this.nextStartTime = currentTime;

        // Notify callback that audio started
        if (this.callbacks?.onAudioReceived) {
          this.callbacks.onAudioReceived(audioBuffer);
        }

        console.log('Started streaming audio from Gemini Live');
      }

      // Start this chunk at the calculated time
      source.start(this.nextStartTime);

      // Track this source for potential interruption
      this.activeSources.push(source);

      // Update next start time for seamless playback
      this.nextStartTime += audioBuffer.duration;

      // Set up completion handler
      source.onended = () => {
        // Remove from active sources
        const index = this.activeSources.indexOf(source);
        if (index > -1) {
          this.activeSources.splice(index, 1);
        }

        // Check if all audio has finished
        if (this.activeSources.length === 0) {
          this.isPlayingAudio = false;
          console.log('Audio streaming completed');
        }
      };

      console.log('Streamed audio chunk from Gemini Live');

    } catch (error) {
      console.error('Failed to stream audio chunk:', error);
    }
  }

  /**
   * Disconnect from the session
   */
  async disconnect(): Promise<void> {
    try {
      if (this.session) {
        await this.session.close();
        this.session = null;
      }

      this.cleanup();
      console.log('Disconnected from Gemini Live Audio');

    } catch (error) {
      console.error('Error disconnecting:', error);
    }
  }

  /**
   * Stop all currently playing audio sources
   */
  private stopAllAudio(): void {
    this.activeSources.forEach(source => {
      try {
        source.stop();
      } catch (e) {
        // Source might already be stopped
      }
    });
    this.activeSources = [];
    this.isPlayingAudio = false;

    this.nextStartTime = 0;
    console.log('All audio sources stopped');
  }

  /**
   * Clean up resources
   */
  private cleanup(): void {
    // Stop microphone input
    this.stopMicrophoneInput();

    // Stop any currently playing audio
    this.stopAllAudio();

    // Close media stream
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
      this.mediaStream = null;
    }

    // Close audio contexts
    if (this.inputAudioContext && this.inputAudioContext.state !== 'closed') {
      this.inputAudioContext.close().catch(console.error);
      this.inputAudioContext = null;
    }

    if (this.outputAudioContext && this.outputAudioContext.state !== 'closed') {
      this.outputAudioContext.close().catch(console.error);
      this.outputAudioContext = null;
    }

    this.callbacks = null;
    this.responseQueue = [];
    this.isPlayingAudio = false;

    this.nextStartTime = 0;
  }

  /**
   * Check if currently connected
   */
  isConnected(): boolean {
    return this.session !== null;
  }

  /**
   * Stop any currently playing audio
   */
  stopAudio(): void {
    this.stopAllAudio();
  }

  /**
   * Check if audio is currently playing
   */
  isAudioPlaying(): boolean {
    return this.isPlayingAudio;
  }
}
